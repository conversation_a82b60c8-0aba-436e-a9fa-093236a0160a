from django.db import models


class Parameter(models.Model):
    parameter_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50)
    value = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'parameter'

    def __str__(self):
        return self.name


class Regions(models.Model):
    code = models.CharField(primary_key=True, max_length=2)
    name = models.CharField(max_length=50)

    class Meta:
        managed = False
        db_table = 'regions'


class Cities(models.Model):
    city_id = models.AutoField(primary_key=True)
    description = models.CharField(max_length=50)
    city_code = models.CharField(max_length=10, blank=True, null=True)
    province = models.CharField(max_length=5, blank=True, null=True)
    region = models.ForeignKey(Regions, models.DO_NOTHING, db_column='region', blank=True, null=True)
    is_city = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        db_table = 'cities'


class Contact(models.Model):
    contact_id = models.AutoField(primary_key=True)
    address = models.TextField(blank=True, null=True)
    phone_num = models.TextField(blank=True, null=True)
    fax = models.TextField(blank=True, null=True)
    city = models.ForeignKey(Cities, models.DO_NOTHING, blank=True, null=True)
    email = models.TextField(blank=True, null=True)
    mobile = models.TextField(blank=True, null=True)
    web = models.TextField(blank=True, null=True)
    cap = models.CharField(max_length=5, blank=True, null=True)

    class Meta:
        db_table = 'contact'
