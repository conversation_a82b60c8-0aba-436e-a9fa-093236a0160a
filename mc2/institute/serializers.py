from rest_framework import serializers

from .models import Institute


class InstituteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Institute
        fields = [
            "institute_id",
            "name",
            "mechan_code",
            "fiscal_code",
            "school_fiscal_code",
            "school_type",
            "postal_account",
            "job_director_id",
            "job_vice_director_id",
            "job_dsga_id",
            "job_personnel_id",
            "job_accounting_id",
            "job_warehouse_id",
            "job_registry_id",
            "contact_id",
            "ipa_code",
            "address",
            "phone_num",
            "fax",
            "email",
            "mobile",
            "web",
            "city_id",
            "cap",
            "ade_email",
            "city_code",
            "province",
            "region",
            "is_city",
            "def_field"
        ]
